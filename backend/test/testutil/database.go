package testutil

import (
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/require"

	"backend/internal/config"
	"backend/internal/database"
	"backend/internal/migrations"
	"backend/internal/repository"
)

// SetupTestDB sets up a test database and returns a repository
func SetupTestDB(t *testing.T) (repository.TodoRepository, func()) {
	// Always use PostgreSQL for testing
	return setupPostgresTestDB(t)
}

// setupPostgresTestDB sets up a PostgreSQL test database
func setupPostgresTestDB(t *testing.T) (repository.TodoRepository, func()) {
	// Skip tests if no database is available
	if os.Getenv("SKIP_DB_TESTS") == "true" {
		t.Skip("Database tests skipped (SKIP_DB_TESTS=true)")
	}

	// Load configuration from environment variables
	cfg := config.Load()

	// Override with test-specific database settings if environment variables are set
	if testHost := os.Getenv("TEST_DB_HOST"); testHost != "" {
		cfg.Database.Host = testHost
	}
	if testName := os.Getenv("TEST_DB_NAME"); testName != "" {
		cfg.Database.Name = testName
	}
	if testUser := os.Getenv("TEST_DB_USER"); testUser != "" {
		cfg.Database.User = testUser
	}
	if testPassword := os.Getenv("TEST_DB_PASSWORD"); testPassword != "" {
		cfg.Database.Password = testPassword
	}

	// Ensure reasonable connection limits for tests
	cfg.Database.MaxConns = 5
	cfg.Database.MinConns = 1

	// Create database connection
	db, err := database.NewPostgresDB(&cfg.Database)
	if err != nil {
		t.Skipf("Skipping database tests: Failed to connect to database: %v", err)
	}

	// Run migrations
	migrator := migrations.NewMigrator(db.Pool)
	err = migrator.Up(context.Background())
	require.NoError(t, err, "Failed to run migrations")

	// Create repository
	repo := repository.NewPostgresTodoRepository(db.Pool)

	// Return cleanup function
	cleanup := func() {
		// Clean up test data - use a transaction to ensure atomicity
		ctx := context.Background()
		tx, err := db.Pool.Begin(ctx)
		if err == nil {
			_, _ = tx.Exec(ctx, "DELETE FROM todos")
			_ = tx.Commit(ctx)
		}
		db.Close()
	}

	return repo, cleanup
}

// CleanupTestDB cleans up test data from the database
func CleanupTestDB(t *testing.T, db *database.PostgresDB) {
	_, err := db.Pool.Exec(context.Background(), "DELETE FROM todos")
	require.NoError(t, err, "Failed to clean up test data")
}
